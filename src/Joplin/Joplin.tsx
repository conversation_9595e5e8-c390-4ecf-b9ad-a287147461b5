import { useContext, useEffect, useState } from "react";
import axios from "axios";
import { filterAndSplit, hv, resolveInOrder } from "../Tools";
import { MsapiClientContext } from "../GraphService";
import { Client } from "@microsoft/microsoft-graph-client";
import { formatISO } from "date-fns";
import {
  Jo<PERSON>linNote,
  createJoplinUrl,
  fetchLongJ<PERSON>linList,
  joplinNoteFieldsValue,
} from "./JoplinShared";
import {
  clearInProgressSectionData,
  fetchInProgressSectionData,
  importOneNoteSection,
} from "./importOneNoteSection";
import { config } from "../Config";
import { moveOrphanedNotesToUnfiled } from "../moveOrphanedNotesToUnfiled";
import { getTagsMessage, processUnfiledNotes } from "../processUnfiledNotes";
import { createBookmarkHtmlFileFromJoplinFolders } from "./createHtmlFileFromJoplinFolders";
import { J_UNFILED_FOLDER_ID } from "./Constants";
import { TAG_ID_PAIRINGS } from "../PairingHelpers";

export function Joplin() {
  const client = useContext(MsapiClientContext);
  const [inProgressImportSectionId, setInProgressImportSectionId] = useState<
    string | null
  >(null);

  useEffect(() => {
    void fetchInProgressSectionData().then(data => {
      setInProgressImportSectionId(data.sectionId);
    });

    void verifyJoplinIds();
  }, []);

  return (
    <>
      <button
        className="border p-1 m-1"
        onClick={() => processJoplinUnfiledNotes()}
      >
        Process Joplin Unfiled Notes
      </button>
      <button
        className="border p-1 m-1"
        onClick={() => createBookmarkHtmlFileFromJoplinFolders()}
      >
        Create folder HTML file
      </button>
      {config.shouldLogInToMsGraph && (
        <>
          {hv(inProgressImportSectionId) ? (
            <>
              <button
                className="border p-1 m-1"
                onClick={() => {
                  void importOneNoteSection(client, inProgressImportSectionId);
                }}
              >
                CONTINUE importing section {inProgressImportSectionId}
              </button>
              <button
                className="border p-1 m-1"
                onClick={async () => {
                  await clearInProgressSectionData();
                  setInProgressImportSectionId(null);
                }}
              >
                RESET import
              </button>
            </>
          ) : (
            <>
              <button
                className="border p-1 m-1"
                onClick={async () => {
                  const sectionId = window.prompt(
                    "OneNote section ID (e.g. 0-A0AC2C4AD3A11111!33333)",
                  );
                  if (!hv(sectionId) || sectionId.length === 0) {
                    window.alert("Canceled");
                    return;
                  }
                  await importOneNoteSection(client, sectionId.trim());
                }}
              >
                Import OneNote Section to Current
              </button>
              <button
                className="border p-1 m-1"
                onClick={async () => {
                  await listAllOneNoteSections(client);
                }}
              >
                List All OneNote Sections
              </button>
            </>
          )}
        </>
      )}
    </>
  );
}

const unfiledNotesToPullCount: number | undefined = undefined;

async function processJoplinUnfiledNotes() {
  if (config.shouldCheckOrphanedPages) {
    await moveOrphanedNotesToUnfiled();
  }

  const limitObj = hv(unfiledNotesToPullCount)
    ? { limit: unfiledNotesToPullCount.toString() }
    : {};

  const options: { fields: string; limit?: string } = {
    fields: joplinNoteFieldsValue,
    ...limitObj,
  };

  console.log("Fetching unfiled...");

  const notes = await fetchLongJoplinList<JoplinNote>(
    `folders/${J_UNFILED_FOLDER_ID}/notes`,
    options,
  );

  const [contentNotes, emptyNotes] = filterAndSplit(
    notes,
    n => n.body.trim().length > 0,
  );

  const handleDelete = async (ids: string[]) => {
    if (ids.length === 0) {
      return;
    }
    console.log(`Deleting ${ids.length} note(s)...`);
    await resolveInOrder(ids, async (id: string) => {
      const patchUrl = await createJoplinUrl(`notes/${id}`);
      await axios.delete(patchUrl);
    });
  };

  if (emptyNotes.length > 0) {
    console.log("Found empty...");
    await handleDelete(emptyNotes.map(n => n.id));
  }

  const allResults = await processUnfiledNotes(
    contentNotes.map(n => ({
      noteText: n.body,
      date: formatISO(n.user_created_time),
      id: n.id,
    })),
    handleDelete,
  );

  console.log(
    `*** Joplin unfiled notes finished. ${getTagsMessage(allResults)}`,
  );
}

export async function prependJoplinPageContent(
  content: string,
  pageId: string,
) {
  const pageUrl = await createJoplinUrl(`notes/${pageId}`, { fields: "body" });
  const note = await axios.get(pageUrl);
  await axios.put(pageUrl, {
    body: `${content.trim()}\n\n${note.data.body.trim()}`,
  });
}

// Only run this once
let didRunVerifyIds = false;

export async function verifyJoplinIds() {
  // Only do this if logging in, avoids a race condition in App top level that
  // I'm not fixing for now Aug 2024
  if (didRunVerifyIds || !config.shouldLogInToMsGraph) {
    return;
  }
  didRunVerifyIds = true;
  // Much faster and no errors in parallel than in serial
  // (2.5 vs 9 seconds)
  // But since it's running in background I'll run in parallel to not freeze UI
  await resolveInOrder(TAG_ID_PAIRINGS, async p => {
    if (hv(p.folderId)) {
      try {
        await axios.get(
          await createJoplinUrl(`folders/${p.folderId}`, {
            fields: "id",
          }),
        );
      } catch {
        console.error(`Error fetching Joplin folder: ${p.folderId}`);
      }
    } else {
      try {
        await axios.get(
          await createJoplinUrl(`notes/${p.noteId}`, { fields: "id" }),
        );
      } catch {
        console.error(`Error fetching Joplin note: ${p.noteId}`);
      }
    }
  });

  console.log("(All Joplin IDs verified)");
}

async function listAllOneNoteSections(client: Client) {
  try {
    console.log("Fetching all OneNote sections...");

    // Fetch all sections from Microsoft Graph API
    const response = await client.api("/me/onenote/sections").get();

    console.log("=== ALL ONENOTE SECTIONS ===");
    console.log(`Found ${response.value.length} sections:`);
    console.log("");

    // Log each section with its details
    response.value.forEach((section: any, index: number) => {
      console.log(`${index + 1}. ${section.displayName}`);
      console.log(`   ID: ${section.id}`);
      console.log(`   Created: ${section.createdDateTime}`);
      console.log(`   Modified: ${section.lastModifiedDateTime}`);
      if (hv(section.parentNotebook)) {
        console.log(`   Notebook: ${section.parentNotebook.displayName}`);
      }
      if (hv(section.parentSectionGroup)) {
        console.log(
          `   Section Group: ${section.parentSectionGroup.displayName}`,
        );
      }
      console.log("");
    });

    console.log("=== RAW API RESPONSE ===");
    console.log(JSON.stringify(response, null, 2));

    // Also show a summary in an alert for easy access
    const sectionList = response.value
      .map(
        (section: any, index: number) =>
          `${index + 1}. ${section.displayName}\n   ID: ${section.id}`,
      )
      .join("\n\n");
  } catch (error) {
    console.error("Error fetching OneNote sections:", error);
    window.alert(`Error fetching sections: ${(error as Error).message}`);
  }
}
